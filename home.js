// Data
const banners = [
    {
        id: 1,
        title: "Khuyến mãi mùa hè",
        description: "Giảm gi<PERSON> đến 50% cho tất cả sản phẩm",
        imageUrl: "https://readdy.ai/api/search-image?query=modern%20e-commerce%20summer%20promotion%20banner%20with%20vibrant%20colors%2C%20showing%20discounts%20and%20summer%20products%2C%20professional%20photography%20style%20with%20clean%20background%20and%20high%20contrast%2C%20perfect%20for%20online%20shopping%20website&width=1440&height=500&seq=banner1&orientation=landscape"
    },
    {
        id: 2,
        title: "Bộ sưu tập mới",
        description: "Khám phá các sản phẩm mới nhất của chúng tôi",
        imageUrl: "https://readdy.ai/api/search-image?query=modern%20e-commerce%20new%20collection%20banner%20with%20stylish%20products%2C%20elegant%20design%2C%20professional%20photography%20with%20clean%20background%2C%20perfect%20for%20online%20shopping%20website%2C%20high-end%20products%20display&width=1440&height=500&seq=banner2&orientation=landscape"
    },
    {
        id: 3,
        title: "Miễn phí vận chuyển",
        description: "Cho đơn hàng trên 500.000đ",
        imageUrl: "https://readdy.ai/api/search-image?query=modern%20e-commerce%20free%20shipping%20promotion%20banner%20with%20delivery%20boxes%20and%20trucks%2C%20professional%20photography%20with%20clean%20background%2C%20perfect%20for%20online%20shopping%20website%2C%20shipping%20and%20logistics%20concept&width=1440&height=500&seq=banner3&orientation=landscape"
    }
];

const categories = [
    {
        id: 1,
        name: "Điện thoại",
        icon: "fa-mobile-alt",
        imageUrl: "https://readdy.ai/api/search-image?query=modern%20smartphone%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20mobile%20phone%20display%20for%20e-commerce&width=200&height=200&seq=cat1&orientation=squarish"
    },
    {
        id: 2,
        name: "Laptop",
        icon: "fa-laptop",
        imageUrl: "https://readdy.ai/api/search-image?query=modern%20laptop%20computer%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20laptop%20display%20for%20e-commerce&width=200&height=200&seq=cat2&orientation=squarish"
    },
    {
        id: 3,
        name: "Máy tính bảng",
        icon: "fa-tablet-alt",
        imageUrl: "https://readdy.ai/api/search-image?query=modern%20tablet%20device%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20tablet%20display%20for%20e-commerce&width=200&height=200&seq=cat3&orientation=squarish"
    },
    {
        id: 4,
        name: "Tai nghe",
        icon: "fa-headphones",
        imageUrl: "https://readdy.ai/api/search-image?query=modern%20headphones%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20audio%20equipment%20display%20for%20e-commerce&width=200&height=200&seq=cat4&orientation=squarish"
    },
    {
        id: 5,
        name: "Đồng hồ thông minh",
        icon: "fa-clock",
        imageUrl: "https://readdy.ai/api/search-image?query=modern%20smartwatch%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20wearable%20technology%20display%20for%20e-commerce&width=200&height=200&seq=cat5&orientation=squarish"
    },
    {
        id: 6,
        name: "Phụ kiện",
        icon: "fa-plug",
        imageUrl: "https://readdy.ai/api/search-image?query=modern%20tech%20accessories%20collection%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20gadget%20accessories%20display%20for%20e-commerce&width=200&height=200&seq=cat6&orientation=squarish"
    },
    {
        id: 7,
        name: "Máy ảnh",
        icon: "fa-camera",
        imageUrl: "https://readdy.ai/api/search-image?query=modern%20digital%20camera%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20photography%20equipment%20display%20for%20e-commerce&width=200&height=200&seq=cat7&orientation=squarish"
    },
    {
        id: 8,
        name: "Gia dụng",
        icon: "fa-blender",
        imageUrl: "https://readdy.ai/api/search-image?query=modern%20home%20appliances%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20household%20equipment%20display%20for%20e-commerce&width=200&height=200&seq=cat8&orientation=squarish"
    }
];

const flashSaleProducts = [
    {
        id: 1,
        name: "Điện thoại Samsung Galaxy S23",
        oldPrice: "25.990.000đ",
        newPrice: "20.790.000đ",
        discount: 20,
        imageUrl: "https://readdy.ai/api/search-image?query=samsung%20galaxy%20s23%20smartphone%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20mobile%20phone%20for%20e-commerce%20display&width=250&height=250&seq=flash1&orientation=squarish"
    },
    {
        id: 2,
        name: "Laptop Dell XPS 13",
        oldPrice: "32.990.000đ",
        newPrice: "28.990.000đ",
        discount: 12,
        imageUrl: "https://readdy.ai/api/search-image?query=dell%20xps%2013%20laptop%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20laptop%20for%20e-commerce%20display&width=250&height=250&seq=flash2&orientation=squarish"
    },
    {
        id: 3,
        name: "Tai nghe Apple AirPods Pro",
        oldPrice: "6.790.000đ",
        newPrice: "5.290.000đ",
        discount: 22,
        imageUrl: "https://readdy.ai/api/search-image?query=apple%20airpods%20pro%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20earbuds%20for%20e-commerce%20display&width=250&height=250&seq=flash3&orientation=squarish"
    },
    {
        id: 4,
        name: "Apple Watch Series 8",
        oldPrice: "10.990.000đ",
        newPrice: "8.990.000đ",
        discount: 18,
        imageUrl: "https://readdy.ai/api/search-image?query=apple%20watch%20series%208%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20smartwatch%20for%20e-commerce%20display&width=250&height=250&seq=flash4&orientation=squarish"
    },
    {
        id: 5,
        name: "iPad Air 5",
        oldPrice: "16.990.000đ",
        newPrice: "14.490.000đ",
        discount: 15,
        imageUrl: "https://readdy.ai/api/search-image?query=ipad%20air%205%20tablet%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20tablet%20for%20e-commerce%20display&width=250&height=250&seq=flash5&orientation=squarish"
    }
];

const featuredProducts = [
    {
        id: 1,
        name: "iPhone 14 Pro Max",
        price: "27.990.000đ",
        rating: 4.9,
        reviews: 128,
        imageUrl: "https://readdy.ai/api/search-image?query=iphone%2014%20pro%20max%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20smartphone%20for%20e-commerce%20display&width=300&height=300&seq=feat1&orientation=squarish"
    },
    {
        id: 2,
        name: "MacBook Pro M2",
        price: "35.990.000đ",
        rating: 4.8,
        reviews: 95,
        imageUrl: "https://readdy.ai/api/search-image?query=macbook%20pro%20m2%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20laptop%20for%20e-commerce%20display&width=300&height=300&seq=feat2&orientation=squarish"
    },
    {
        id: 3,
        name: "Sony WH-1000XM5",
        price: "8.490.000đ",
        rating: 4.7,
        reviews: 76,
        imageUrl: "https://readdy.ai/api/search-image?query=sony%20wh-1000xm5%20headphones%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20audio%20equipment%20for%20e-commerce%20display&width=300&height=300&seq=feat3&orientation=squarish"
    },
    {
        id: 4,
        name: "Samsung Galaxy Tab S8 Ultra",
        price: "24.990.000đ",
        rating: 4.6,
        reviews: 64,
        imageUrl: "https://readdy.ai/api/search-image?query=samsung%20galaxy%20tab%20s8%20ultra%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20tablet%20for%20e-commerce%20display&width=300&height=300&seq=feat4&orientation=squarish"
    },
    {
        id: 5,
        name: "Canon EOS R6",
        price: "52.990.000đ",
        rating: 4.9,
        reviews: 42,
        imageUrl: "https://readdy.ai/api/search-image?query=canon%20eos%20r6%20camera%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20digital%20camera%20for%20e-commerce%20display&width=300&height=300&seq=feat5&orientation=squarish"
    },
    {
        id: 6,
        name: "DJI Mini 3 Pro",
        price: "18.990.000đ",
        rating: 4.7,
        reviews: 53,
        imageUrl: "https://readdy.ai/api/search-image?query=dji%20mini%203%20pro%20drone%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20drone%20for%20e-commerce%20display&width=300&height=300&seq=feat6&orientation=squarish"
    },
    {
        id: 7,
        name: "Bose QuietComfort Earbuds II",
        price: "7.990.000đ",
        rating: 4.6,
        reviews: 38,
        imageUrl: "https://readdy.ai/api/search-image?query=bose%20quietcomfort%20earbuds%20ii%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20earbuds%20for%20e-commerce%20display&width=300&height=300&seq=feat7&orientation=squarish"
    },
    {
        id: 8,
        name: "GoPro HERO11 Black",
        price: "10.990.000đ",
        rating: 4.8,
        reviews: 47,
        imageUrl: "https://readdy.ai/api/search-image?query=gopro%20hero11%20black%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20action%20camera%20for%20e-commerce%20display&width=300&height=300&seq=feat8&orientation=squarish"
    }
];

// Global variables
let currentSlide = 0;
let timeLeft = {
    hours: 2,
    minutes: 30,
    seconds: 0
};

// Banner slider functions
function nextSlide() {
    currentSlide = (currentSlide === banners.length - 1) ? 0 : currentSlide + 1;
    updateSlider();
}

function prevSlide() {
    currentSlide = (currentSlide === 0) ? banners.length - 1 : currentSlide - 1;
    updateSlider();
}

function goToSlide(index) {
    currentSlide = index;
    updateSlider();
}

function updateSlider() {
    const slides = document.querySelectorAll('.banner-slide');
    const dots = document.querySelectorAll('.dot');

    slides.forEach((slide, index) => {
        slide.classList.toggle('active', index === currentSlide);
    });

    dots.forEach((dot, index) => {
        dot.classList.toggle('active', index === currentSlide);
    });
}

// Countdown timer function
function updateCountdown() {
    if (timeLeft.seconds > 0) {
        timeLeft.seconds--;
    } else if (timeLeft.minutes > 0) {
        timeLeft.minutes--;
        timeLeft.seconds = 59;
    } else if (timeLeft.hours > 0) {
        timeLeft.hours--;
        timeLeft.minutes = 59;
        timeLeft.seconds = 59;
    }

    document.getElementById('hours').textContent = String(timeLeft.hours).padStart(2, '0');
    document.getElementById('minutes').textContent = String(timeLeft.minutes).padStart(2, '0');
    document.getElementById('seconds').textContent = String(timeLeft.seconds).padStart(2, '0');
}

// Render stars function
function renderStars(rating) {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < fullStars; i++) {
        stars.push('<i class="fas fa-star"></i>');
    }

    if (hasHalfStar) {
        stars.push('<i class="fas fa-star-half-alt"></i>');
    }

    const emptyStars = 5 - stars.length;
    for (let i = 0; i < emptyStars; i++) {
        stars.push('<i class="far fa-star"></i>');
    }

    return stars.join('');
}

// Load categories
function loadCategories() {
    const categoriesGrid = document.getElementById('categoriesGrid');
    if (!categoriesGrid) {
        console.error('Categories grid not found');
        return;
    }

    categoriesGrid.innerHTML = '';

    categories.forEach((category, index) => {
        const categoryElement = document.createElement('a');
        categoryElement.href = '#';
        categoryElement.className = 'category-item';

        const colors = ['#2563eb', '#dc2626', '#059669', '#d97706', '#7c3aed', '#db2777', '#0891b2', '#65a30d'];
        const bgColor = colors[index % colors.length];

        categoryElement.innerHTML = `
            <div class="category-image">
                <div style="width: 64px; height: 64px; background: ${bgColor}; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 24px;">
                    <i class="${category.icon}"></i>
                </div>
            </div>
            <span class="category-name">${category.name}</span>
        `;
        categoriesGrid.appendChild(categoryElement);
    });
}

// Load flash sale products
function loadFlashSaleProducts() {
    const flashSaleGrid = document.getElementById('flashSaleProducts');
    if (!flashSaleGrid) {
        console.error('Flash sale grid not found');
        return;
    }

    flashSaleGrid.innerHTML = '';

    flashSaleProducts.forEach((product, index) => {
        const productElement = document.createElement('div');
        productElement.className = 'product-card flash-sale';

        const soldPercentage = Math.floor(Math.random() * 30) + 70;
        const soldCount = Math.floor(Math.random() * 50) + 50;

        const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6'];
        const bgColor = colors[index % colors.length];

        productElement.innerHTML = `
            <div class="discount-badge">-${product.discount}%</div>
            <div class="product-image">
                <div style="width: 144px; height: 144px; background: ${bgColor}; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 14px; text-align: center; padding: 10px;">
                    ${product.name.split(' ')[0]}
                </div>
            </div>
            <h3 class="product-name">${product.name}</h3>
            <div class="product-price">
                <span class="new-price">${product.newPrice}</span>
                <span class="old-price">${product.oldPrice}</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${soldPercentage}%"></div>
            </div>
            <div class="sold-count">Đã bán ${soldCount}</div>
            <button class="product-btn">Mua ngay</button>
        `;
        flashSaleGrid.appendChild(productElement);
    });
}

// Load featured products
function loadFeaturedProducts() {
    const featuredGrid = document.getElementById('featuredProducts');
    if (!featuredGrid) {
        console.error('Featured grid not found');
        return;
    }

    featuredGrid.innerHTML = '';

    featuredProducts.forEach((product, index) => {
        const productElement = document.createElement('div');
        productElement.className = 'product-card';

        const colors = ['#1f2937', '#374151', '#4b5563', '#6b7280', '#9ca3af', '#d1d5db', '#e5e7eb', '#f3f4f6'];
        const bgColor = colors[index % colors.length];

        productElement.innerHTML = `
            <div class="product-image">
                <div style="width: 192px; height: 192px; background: ${bgColor}; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px; text-align: center; padding: 15px;">
                    ${product.name.split(' ')[0]}
                </div>
            </div>
            <h3 class="product-name">${product.name}</h3>
            <div class="single-price">${product.price}</div>
            <div class="rating">
                <div class="stars">${renderStars(product.rating)}</div>
                <span class="review-count">(${product.reviews})</span>
            </div>
            <button class="product-btn featured">
                <i class="fas fa-shopping-cart"></i> Thêm vào giỏ hàng
            </button>
        `;
        featuredGrid.appendChild(productElement);
    });
}

// Initialize the page
function init() {
    console.log('Initializing page...');

    try {
        loadCategories();
        console.log('Categories loaded');

        loadFlashSaleProducts();
        console.log('Flash sale products loaded');

        loadFeaturedProducts();
        console.log('Featured products loaded');

        // Start countdown timer
        setInterval(updateCountdown, 1000);

        // Start auto slider
        setInterval(nextSlide, 5000);

        console.log('Page initialization complete');
    } catch (error) {
        console.error('Error during initialization:', error);
    }
}

// Run when page loads
document.addEventListener('DOMContentLoaded', init);

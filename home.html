// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState, useEffect } from "react";
const App: React.FC = () => {
  // State for countdown timer
  const [timeLeft, setTimeLeft] = useState({
    hours: 2,
    minutes: 30,
    seconds: 0,
  });
  // State for active slide
  const [activeSlide, setActiveSlide] = useState(0);
  // Update countdown timer
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime.seconds > 0) {
          return { ...prevTime, seconds: prevTime.seconds - 1 };
        } else if (prevTime.minutes > 0) {
          return { ...prevTime, minutes: prevTime.minutes - 1, seconds: 59 };
        } else if (prevTime.hours > 0) {
          return {
            ...prevTime,
            hours: prevTime.hours - 1,
            minutes: 59,
            seconds: 59,
          };
        } else {
          clearInterval(timer);
          return prevTime;
        }
      });
    }, 1000);
    return () => clearInterval(timer);
  }, []);
  // Banners data
  const banners = [
    {
      id: 1,
      title: "Khuyến mãi mùa hè",
      description: "Giảm giá đến 50% cho tất cả sản phẩm",
      imageUrl:
        "https://readdy.ai/api/search-image?query=modern%20e-commerce%20summer%20promotion%20banner%20with%20vibrant%20colors%2C%20showing%20discounts%20and%20summer%20products%2C%20professional%20photography%20style%20with%20clean%20background%20and%20high%20contrast%2C%20perfect%20for%20online%20shopping%20website&width=1440&height=500&seq=banner1&orientation=landscape",
    },
    {
      id: 2,
      title: "Bộ sưu tập mới",
      description: "Khám phá các sản phẩm mới nhất của chúng tôi",
      imageUrl:
        "https://readdy.ai/api/search-image?query=modern%20e-commerce%20new%20collection%20banner%20with%20stylish%20products%2C%20elegant%20design%2C%20professional%20photography%20with%20clean%20background%2C%20perfect%20for%20online%20shopping%20website%2C%20high-end%20products%20display&width=1440&height=500&seq=banner2&orientation=landscape",
    },
    {
      id: 3,
      title: "Miễn phí vận chuyển",
      description: "Cho đơn hàng trên 500.000đ",
      imageUrl:
        "https://readdy.ai/api/search-image?query=modern%20e-commerce%20free%20shipping%20promotion%20banner%20with%20delivery%20boxes%20and%20trucks%2C%20professional%20photography%20with%20clean%20background%2C%20perfect%20for%20online%20shopping%20website%2C%20shipping%20and%20logistics%20concept&width=1440&height=500&seq=banner3&orientation=landscape",
    },
  ];
  // Categories data
  const categories = [
    {
      id: 1,
      name: "Điện thoại",
      icon: "fa-mobile-alt",
      imageUrl:
        "https://readdy.ai/api/search-image?query=modern%20smartphone%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20mobile%20phone%20display%20for%20e-commerce&width=200&height=200&seq=cat1&orientation=squarish",
    },
    {
      id: 2,
      name: "Laptop",
      icon: "fa-laptop",
      imageUrl:
        "https://readdy.ai/api/search-image?query=modern%20laptop%20computer%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20laptop%20display%20for%20e-commerce&width=200&height=200&seq=cat2&orientation=squarish",
    },
    {
      id: 3,
      name: "Máy tính bảng",
      icon: "fa-tablet-alt",
      imageUrl:
        "https://readdy.ai/api/search-image?query=modern%20tablet%20device%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20tablet%20display%20for%20e-commerce&width=200&height=200&seq=cat3&orientation=squarish",
    },
    {
      id: 4,
      name: "Tai nghe",
      icon: "fa-headphones",
      imageUrl:
        "https://readdy.ai/api/search-image?query=modern%20headphones%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20audio%20equipment%20display%20for%20e-commerce&width=200&height=200&seq=cat4&orientation=squarish",
    },
    {
      id: 5,
      name: "Đồng hồ thông minh",
      icon: "fa-clock",
      imageUrl:
        "https://readdy.ai/api/search-image?query=modern%20smartwatch%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20wearable%20technology%20display%20for%20e-commerce&width=200&height=200&seq=cat5&orientation=squarish",
    },
    {
      id: 6,
      name: "Phụ kiện",
      icon: "fa-plug",
      imageUrl:
        "https://readdy.ai/api/search-image?query=modern%20tech%20accessories%20collection%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20gadget%20accessories%20display%20for%20e-commerce&width=200&height=200&seq=cat6&orientation=squarish",
    },
    {
      id: 7,
      name: "Máy ảnh",
      icon: "fa-camera",
      imageUrl:
        "https://readdy.ai/api/search-image?query=modern%20digital%20camera%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20photography%20equipment%20display%20for%20e-commerce&width=200&height=200&seq=cat7&orientation=squarish",
    },
    {
      id: 8,
      name: "Gia dụng",
      icon: "fa-blender",
      imageUrl:
        "https://readdy.ai/api/search-image?query=modern%20home%20appliances%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20household%20equipment%20display%20for%20e-commerce&width=200&height=200&seq=cat8&orientation=squarish",
    },
  ];
  // Flash sale products
  const flashSaleProducts = [
    {
      id: 1,
      name: "Điện thoại Samsung Galaxy S23",
      oldPrice: "25.990.000đ",
      newPrice: "20.790.000đ",
      discount: 20,
      imageUrl:
        "https://readdy.ai/api/search-image?query=samsung%20galaxy%20s23%20smartphone%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20mobile%20phone%20for%20e-commerce%20display&width=250&height=250&seq=flash1&orientation=squarish",
    },
    {
      id: 2,
      name: "Laptop Dell XPS 13",
      oldPrice: "32.990.000đ",
      newPrice: "28.990.000đ",
      discount: 12,
      imageUrl:
        "https://readdy.ai/api/search-image?query=dell%20xps%2013%20laptop%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20laptop%20for%20e-commerce%20display&width=250&height=250&seq=flash2&orientation=squarish",
    },
    {
      id: 3,
      name: "Tai nghe Apple AirPods Pro",
      oldPrice: "6.790.000đ",
      newPrice: "5.290.000đ",
      discount: 22,
      imageUrl:
        "https://readdy.ai/api/search-image?query=apple%20airpods%20pro%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20earbuds%20for%20e-commerce%20display&width=250&height=250&seq=flash3&orientation=squarish",
    },
    {
      id: 4,
      name: "Apple Watch Series 8",
      oldPrice: "10.990.000đ",
      newPrice: "8.990.000đ",
      discount: 18,
      imageUrl:
        "https://readdy.ai/api/search-image?query=apple%20watch%20series%208%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20smartwatch%20for%20e-commerce%20display&width=250&height=250&seq=flash4&orientation=squarish",
    },
    {
      id: 5,
      name: "iPad Air 5",
      oldPrice: "16.990.000đ",
      newPrice: "14.490.000đ",
      discount: 15,
      imageUrl:
        "https://readdy.ai/api/search-image?query=ipad%20air%205%20tablet%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20tablet%20for%20e-commerce%20display&width=250&height=250&seq=flash5&orientation=squarish",
    },
  ];
  // Featured products
  const featuredProducts = [
    {
      id: 1,
      name: "iPhone 14 Pro Max",
      price: "27.990.000đ",
      rating: 4.9,
      reviews: 128,
      imageUrl:
        "https://readdy.ai/api/search-image?query=iphone%2014%20pro%20max%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20smartphone%20for%20e-commerce%20display&width=300&height=300&seq=feat1&orientation=squarish",
    },
    {
      id: 2,
      name: "MacBook Pro M2",
      price: "35.990.000đ",
      rating: 4.8,
      reviews: 95,
      imageUrl:
        "https://readdy.ai/api/search-image?query=macbook%20pro%20m2%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20laptop%20for%20e-commerce%20display&width=300&height=300&seq=feat2&orientation=squarish",
    },
    {
      id: 3,
      name: "Sony WH-1000XM5",
      price: "8.490.000đ",
      rating: 4.7,
      reviews: 76,
      imageUrl:
        "https://readdy.ai/api/search-image?query=sony%20wh-1000xm5%20headphones%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20audio%20equipment%20for%20e-commerce%20display&width=300&height=300&seq=feat3&orientation=squarish",
    },
    {
      id: 4,
      name: "Samsung Galaxy Tab S8 Ultra",
      price: "24.990.000đ",
      rating: 4.6,
      reviews: 64,
      imageUrl:
        "https://readdy.ai/api/search-image?query=samsung%20galaxy%20tab%20s8%20ultra%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20tablet%20for%20e-commerce%20display&width=300&height=300&seq=feat4&orientation=squarish",
    },
    {
      id: 5,
      name: "Canon EOS R6",
      price: "52.990.000đ",
      rating: 4.9,
      reviews: 42,
      imageUrl:
        "https://readdy.ai/api/search-image?query=canon%20eos%20r6%20camera%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20digital%20camera%20for%20e-commerce%20display&width=300&height=300&seq=feat5&orientation=squarish",
    },
    {
      id: 6,
      name: "DJI Mini 3 Pro",
      price: "18.990.000đ",
      rating: 4.7,
      reviews: 53,
      imageUrl:
        "https://readdy.ai/api/search-image?query=dji%20mini%203%20pro%20drone%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20drone%20for%20e-commerce%20display&width=300&height=300&seq=feat6&orientation=squarish",
    },
    {
      id: 7,
      name: "Bose QuietComfort Earbuds II",
      price: "7.990.000đ",
      rating: 4.6,
      reviews: 38,
      imageUrl:
        "https://readdy.ai/api/search-image?query=bose%20quietcomfort%20earbuds%20ii%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20earbuds%20for%20e-commerce%20display&width=300&height=300&seq=feat7&orientation=squarish",
    },
    {
      id: 8,
      name: "GoPro HERO11 Black",
      price: "10.990.000đ",
      rating: 4.8,
      reviews: 47,
      imageUrl:
        "https://readdy.ai/api/search-image?query=gopro%20hero11%20black%20on%20minimalist%20background%2C%20professional%20product%20photography%20with%20soft%20shadows%2C%20clean%20white%20background%2C%20high-end%20action%20camera%20for%20e-commerce%20display&width=300&height=300&seq=feat8&orientation=squarish",
    },
  ];
  // Function to render stars based on rating
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <i key={`star-${i}`} className="fas fa-star text-yellow-400"></i>,
      );
    }
    if (hasHalfStar) {
      stars.push(
        <i
          key="half-star"
          className="fas fa-star-half-alt text-yellow-400"
        ></i>,
      );
    }
    const emptyStars = 5 - stars.length;
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <i key={`empty-star-${i}`} className="far fa-star text-yellow-400"></i>,
      );
    }
    return stars;
  };
  // Function to handle banner navigation
  const nextSlide = () => {
    setActiveSlide((prev) => (prev === banners.length - 1 ? 0 : prev + 1));
  };
  const prevSlide = () => {
    setActiveSlide((prev) => (prev === 0 ? banners.length - 1 : prev - 1));
  };
  // Auto slide for banner
  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 5000);
    return () => clearInterval(interval);
  }, [activeSlide]);
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 bg-white shadow-md z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex-shrink-0">
              <a href="#" className="flex items-center">
                <i className="fas fa-shopping-bag text-2xl text-blue-600 mr-2"></i>
                <span className="text-xl font-bold text-gray-800">
                  ShopViet
                </span>
              </a>
            </div>
            {/* Search Bar */}
            <div className="flex-1 max-w-2xl mx-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Tìm kiếm sản phẩm..."
                  className="w-full py-2 pl-4 pr-10 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
                <button className="absolute right-0 top-0 mt-2 mr-3 text-gray-400 hover:text-blue-500">
                  <i className="fas fa-search"></i>
                </button>
              </div>
            </div>
            {/* Icons */}
            <div className="flex items-center space-x-6">
              <a
                href="#"
                className="text-gray-600 hover:text-blue-600 relative cursor-pointer"
              >
                <i className="fas fa-bell text-xl"></i>
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  3
                </span>
              </a>
              <a
                href="#"
                className="text-gray-600 hover:text-blue-600 relative cursor-pointer"
              >
                <i className="fas fa-shopping-cart text-xl"></i>
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  2
                </span>
              </a>
              <a
                href="#"
                className="text-gray-600 hover:text-blue-600 cursor-pointer"
              >
                <i className="fas fa-user-circle text-xl"></i>
              </a>
            </div>
          </div>
        </div>
        {/* Categories Menu */}
        <div className="bg-gray-100 border-t border-gray-200">
          <div className="container mx-auto px-4">
            <div className="flex items-center space-x-8 py-2 overflow-x-auto whitespace-nowrap">
              <a
                href="#"
                className="text-gray-700 hover:text-blue-600 font-medium text-sm cursor-pointer"
              >
                Trang chủ
              </a>
              <a
                href="#"
                className="text-gray-700 hover:text-blue-600 font-medium text-sm cursor-pointer"
              >
                Điện thoại
              </a>
              <a
                href="#"
                className="text-gray-700 hover:text-blue-600 font-medium text-sm cursor-pointer"
              >
                Laptop
              </a>
              <a
                href="#"
                className="text-gray-700 hover:text-blue-600 font-medium text-sm cursor-pointer"
              >
                Máy tính bảng
              </a>
              <a
                href="#"
                className="text-gray-700 hover:text-blue-600 font-medium text-sm cursor-pointer"
              >
                Âm thanh
              </a>
              <a
                href="#"
                className="text-gray-700 hover:text-blue-600 font-medium text-sm cursor-pointer"
              >
                Đồng hồ
              </a>
              <a
                href="#"
                className="text-gray-700 hover:text-blue-600 font-medium text-sm cursor-pointer"
              >
                Nhà thông minh
              </a>
              <a
                href="#"
                className="text-gray-700 hover:text-blue-600 font-medium text-sm cursor-pointer"
              >
                Phụ kiện
              </a>
              <a
                href="#"
                className="text-gray-700 hover:text-blue-600 font-medium text-sm cursor-pointer"
              >
                Máy ảnh
              </a>
              <a
                href="#"
                className="text-gray-700 hover:text-blue-600 font-medium text-sm cursor-pointer"
              >
                Khuyến mãi
              </a>
            </div>
          </div>
        </div>
      </header>
      {/* Main Content */}
      <main className="pt-28 pb-10">
        {/* Banner Slider */}
        <div className="relative mb-8">
          <div className="overflow-hidden h-[500px]">
            {banners.map((banner, index) => (
              <div
                key={banner.id}
                className={`absolute top-0 left-0 w-full h-full transition-opacity duration-500 ${index === activeSlide ? "opacity-100" : "opacity-0"}`}
              >
                <img
                  src={banner.imageUrl}
                  alt={banner.title}
                  className="w-full h-full object-cover object-top"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
                  <div className="container mx-auto px-8">
                    <div className="max-w-lg text-white">
                      <h2 className="text-4xl font-bold mb-4">
                        {banner.title}
                      </h2>
                      <p className="text-xl mb-6">{banner.description}</p>
                      <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-full font-medium transition-colors duration-300 !rounded-button whitespace-nowrap cursor-pointer">
                        Mua ngay
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {/* Navigation Buttons */}
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/30 hover:bg-white/50 w-10 h-10 rounded-full flex items-center justify-center backdrop-blur-sm text-white transition-all duration-300 !rounded-button whitespace-nowrap cursor-pointer"
          >
            <i className="fas fa-chevron-left"></i>
          </button>
          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/30 hover:bg-white/50 w-10 h-10 rounded-full flex items-center justify-center backdrop-blur-sm text-white transition-all duration-300 !rounded-button whitespace-nowrap cursor-pointer"
          >
            <i className="fas fa-chevron-right"></i>
          </button>
          {/* Pagination Dots */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
            {banners.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === activeSlide ? "bg-white scale-110" : "bg-white/50"
                } cursor-pointer`}
              ></button>
            ))}
          </div>
        </div>
        <div className="container mx-auto px-4">
          {/* Featured Categories */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">
              Danh Mục Nổi Bật
            </h2>
            <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-8 gap-4">
              {categories.map((category) => (
                <a
                  key={category.id}
                  href="#"
                  className="bg-white rounded-lg border-2 border-gray-100 shadow-sm hover:shadow-lg hover:border-blue-100 transition-all duration-500 flex flex-col items-center p-4 cursor-pointer transform hover:scale-105"
                >
                  <div className="w-16 h-16 mb-3 overflow-hidden category-image-container">
                    <img
                      src={category.imageUrl}
                      alt={category.name}
                      className="w-full h-full object-cover object-top transition-opacity duration-500"
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    {category.name}
                  </span>
                </a>
              ))}
            </div>
            <style>
              {`
@keyframes fadeInOut {
0% { opacity: 0.4; transform: scale(0.95); }
50% { opacity: 1; transform: scale(1); }
100% { opacity: 0.4; transform: scale(0.95); }
}
.category-image-container {
animation: fadeInOut 3s infinite;
}
.category-image-container:nth-child(odd) {
animation-delay: 1.5s;
}
`}
            </style>
          </section>
          {/* Flash Sale */}
          <section className="mb-12">
            <div className="bg-gradient-to-r from-red-600 to-red-500 rounded-lg p-4 mb-6 flex items-center justify-between">
              <div className="flex items-center">
                <i className="fas fa-bolt text-yellow-300 text-2xl mr-3"></i>
                <h2 className="text-2xl font-bold text-white">FLASH SALE</h2>
              </div>
              <div className="flex items-center space-x-2 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-lg">
                <span className="text-white font-medium">Kết thúc sau:</span>
                <div className="bg-white text-red-600 font-bold rounded px-2 py-1 text-sm">
                  {String(timeLeft.hours).padStart(2, "0")}
                </div>
                <span className="text-white">:</span>
                <div className="bg-white text-red-600 font-bold rounded px-2 py-1 text-sm">
                  {String(timeLeft.minutes).padStart(2, "0")}
                </div>
                <span className="text-white">:</span>
                <div className="bg-white text-red-600 font-bold rounded px-2 py-1 text-sm">
                  {String(timeLeft.seconds).padStart(2, "0")}
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {flashSaleProducts.map((product) => (
                <div
                  key={product.id}
                  className="bg-white rounded-lg border-2 border-gray-100 shadow-sm hover:shadow-lg hover:border-red-100 transition-all duration-300 p-4 relative"
                >
                  <div className="absolute top-2 left-2 bg-red-600 text-white text-xs font-bold px-2 py-1 rounded">
                    -{product.discount}%
                  </div>
                  <div className="mb-3 flex justify-center">
                    <img
                      src={product.imageUrl}
                      alt={product.name}
                      className="w-36 h-36 object-contain"
                    />
                  </div>
                  <h3 className="text-sm font-medium text-gray-800 mb-2 line-clamp-2 h-10">
                    {product.name}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <span className="text-red-600 font-bold">
                      {product.newPrice}
                    </span>
                    <span className="text-gray-400 text-xs line-through">
                      {product.oldPrice}
                    </span>
                  </div>
                  <div className="mt-3 bg-red-100 rounded-full h-2 overflow-hidden">
                    <div
                      className="bg-red-500 h-full rounded-full"
                      style={{
                        width: `${Math.floor(Math.random() * 30) + 70}%`,
                      }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Đã bán {Math.floor(Math.random() * 50) + 50}
                  </div>
                  <button className="mt-3 w-full bg-red-600 hover:bg-red-700 text-white py-2 rounded font-medium text-sm transition-colors duration-300 !rounded-button whitespace-nowrap cursor-pointer">
                    Mua ngay
                  </button>
                </div>
              ))}
            </div>
            <div className="text-center mt-6">
              <a
                href="#"
                className="inline-block border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-6 py-2 rounded-full font-medium transition-colors duration-300 !rounded-button whitespace-nowrap cursor-pointer"
              >
                Xem thêm sản phẩm
              </a>
            </div>
          </section>
          {/* Featured Products */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">
              Sản Phẩm Nổi Bật
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {featuredProducts.map((product) => (
                <div
                  key={product.id}
                  className="bg-white rounded-lg border-2 border-gray-100 shadow-sm hover:shadow-lg hover:border-blue-100 transition-all duration-300 p-4"
                >
                  <div className="mb-4 flex justify-center">
                    <img
                      src={product.imageUrl}
                      alt={product.name}
                      className="w-48 h-48 object-contain"
                    />
                  </div>
                  <h3 className="text-base font-medium text-gray-800 mb-2 line-clamp-2 h-12">
                    {product.name}
                  </h3>
                  <div className="text-red-600 font-bold mb-2">
                    {product.price}
                  </div>
                  <div className="flex items-center mb-3">
                    <div className="flex mr-2">
                      {renderStars(product.rating)}
                    </div>
                    <span className="text-gray-500 text-sm">
                      ({product.reviews})
                    </span>
                  </div>
                  <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded font-medium text-sm transition-colors duration-300 !rounded-button whitespace-nowrap cursor-pointer">
                    <i className="fas fa-shopping-cart mr-2"></i>
                    Thêm vào giỏ hàng
                  </button>
                </div>
              ))}
            </div>
            <div className="text-center mt-8">
              <a
                href="#"
                className="inline-block border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-6 py-2 rounded-full font-medium transition-colors duration-300 !rounded-button whitespace-nowrap cursor-pointer"
              >
                Xem tất cả sản phẩm
              </a>
            </div>
          </section>
        </div>
      </main>
      {/* Footer */}
      <footer className="bg-gray-800 text-white pt-12 pb-6">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
            <div>
              <h3 className="text-lg font-bold mb-4">Về Chúng Tôi</h3>
              <ul className="space-y-2">
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Giới thiệu
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Tuyển dụng
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Tin tức
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Liên hệ
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Hệ thống cửa hàng
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4">Hỗ Trợ Khách Hàng</h3>
              <ul className="space-y-2">
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Hướng dẫn mua hàng
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Phương thức thanh toán
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Chính sách giao hàng
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Chính sách đổi trả
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Câu hỏi thường gặp
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4">Chính Sách</h3>
              <ul className="space-y-2">
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Chính sách bảo mật
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Quy chế hoạt động
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Chính sách vận chuyển
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Chính sách bảo hành
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-white text-sm cursor-pointer"
                  >
                    Chính sách kiểm hàng
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4">Theo Dõi Chúng Tôi</h3>
              <div className="flex space-x-4 mb-4">
                <a
                  href="#"
                  className="bg-blue-600 hover:bg-blue-700 w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-300 cursor-pointer"
                >
                  <i className="fab fa-facebook-f"></i>
                </a>
                <a
                  href="#"
                  className="bg-red-600 hover:bg-red-700 w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-300 cursor-pointer"
                >
                  <i className="fab fa-youtube"></i>
                </a>
                <a
                  href="#"
                  className="bg-pink-600 hover:bg-pink-700 w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-300 cursor-pointer"
                >
                  <i className="fab fa-instagram"></i>
                </a>
                <a
                  href="#"
                  className="bg-blue-400 hover:bg-blue-500 w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-300 cursor-pointer"
                >
                  <i className="fab fa-twitter"></i>
                </a>
              </div>
              <h3 className="text-lg font-bold mb-4">Đăng Ký Nhận Tin</h3>
              <div className="flex">
                <input
                  type="email"
                  placeholder="Email của bạn"
                  className="flex-1 py-2 px-3 rounded-l text-gray-800 border-none focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
                <button className="bg-blue-600 hover:bg-blue-700 px-4 rounded-r transition-colors duration-300 !rounded-button whitespace-nowrap cursor-pointer">
                  <i className="fas fa-paper-plane"></i>
                </button>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-700 pt-6">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-4 md:mb-0">
                <h3 className="text-lg font-bold mb-2">
                  Phương Thức Thanh Toán
                </h3>
                <div className="flex space-x-4">
                  <i className="fab fa-cc-visa text-2xl"></i>
                  <i className="fab fa-cc-mastercard text-2xl"></i>
                  <i className="fab fa-cc-jcb text-2xl"></i>
                  <i className="fab fa-cc-paypal text-2xl"></i>
                  <i className="fas fa-money-bill-wave text-2xl"></i>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-bold mb-2">Đơn Vị Vận Chuyển</h3>
                <div className="flex space-x-4">
                  <i className="fas fa-shipping-fast text-2xl"></i>
                  <i className="fas fa-truck text-2xl"></i>
                  <i className="fas fa-motorcycle text-2xl"></i>
                </div>
              </div>
            </div>
          </div>
          <div className="text-center mt-8 text-gray-400 text-sm">
            <p>© 2025 ShopViet. Tất cả các quyền được bảo lưu.</p>
            <p className="mt-1">
              Địa chỉ: 123 Đường Lê Lợi, Quận 1, TP. Hồ Chí Minh, Việt Nam
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};
export default App;

/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9fafb;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #1f2937;
}

.logo-link i {
    font-size: 1.5rem;
    color: #2563eb;
    margin-right: 0.5rem;
}

.logo-link span {
    font-size: 1.25rem;
    font-weight: bold;
}

.search-container {
    flex: 1;
    max-width: 32rem;
    margin: 0 1rem;
}

.search-wrapper {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.5rem 1rem;
    padding-right: 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 9999px;
    font-size: 0.875rem;
    outline: none;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-btn {
    position: absolute;
    right: 0;
    top: 0;
    margin-top: 0.5rem;
    margin-right: 0.75rem;
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    transition: color 0.3s ease;
}

.search-btn:hover {
    color: #2563eb;
}

.header-icons {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.icon-link {
    position: relative;
    color: #6b7280;
    text-decoration: none;
    transition: color 0.3s ease;
}

.icon-link:hover {
    color: #2563eb;
}

.icon-link i {
    font-size: 1.25rem;
}

.badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Categories Menu */
.categories-menu {
    background: #f3f4f6;
    border-top: 1px solid #e5e7eb;
}

.categories-list {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 0.5rem 0;
    overflow-x: auto;
    white-space: nowrap;
}

.category-link {
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.category-link:hover {
    color: #2563eb;
}

/* Main Content */
.main-content {
    padding-top: 7rem;
    padding-bottom: 2.5rem;
}

/* Banner Slider */
.banner-slider {
    position: relative;
    margin-bottom: 2rem;
    height: 500px;
    overflow: hidden;
}

.banner-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.banner-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.banner-slide.active {
    opacity: 1;
}

.banner-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, rgba(0,0,0,0.6), transparent);
    display: flex;
    align-items: center;
}

.banner-content {
    max-width: 28rem;
    color: white;
    padding: 0 2rem;
}

.banner-content h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.banner-content p {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
}

.btn-primary {
    background: #2563eb;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 9999px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background: #1d4ed8;
}

.banner-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.3);
    backdrop-filter: blur(4px);
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.banner-nav:hover {
    background: rgba(255,255,255,0.5);
}

.banner-nav.prev {
    left: 1rem;
}

.banner-nav.next {
    right: 1rem;
}

.banner-dots {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: rgba(255,255,255,0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: white;
    transform: scale(1.1);
}

/* Sections */
.section {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.section-footer {
    text-align: center;
    margin-top: 2rem;
}

.btn-outline {
    display: inline-block;
    border: 1px solid #2563eb;
    color: #2563eb;
    padding: 0.5rem 1.5rem;
    border-radius: 9999px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: #2563eb;
    color: white;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

@media (min-width: 640px) {
    .categories-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 1024px) {
    .categories-grid {
        grid-template-columns: repeat(8, 1fr);
    }
}

.category-item {
    background: white;
    border: 2px solid #f3f4f6;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 1rem;
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.5s ease;
    transform: scale(1);
}

.category-item:hover {
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    border-color: #dbeafe;
    transform: scale(1.05);
}

.category-image {
    width: 64px;
    height: 64px;
    margin-bottom: 0.75rem;
    overflow: hidden;
    animation: fadeInOut 3s infinite;
}

.category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.5s ease;
}

.category-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

@keyframes fadeInOut {
    0% { opacity: 0.4; transform: scale(0.95); }
    50% { opacity: 1; transform: scale(1); }
    100% { opacity: 0.4; transform: scale(0.95); }
}

.category-image:nth-child(odd) {
    animation-delay: 1.5s;
}

/* Flash Sale */
.flash-sale-header {
    background: linear-gradient(to right, #dc2626, #ef4444);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flash-sale-title {
    display: flex;
    align-items: center;
    color: white;
}

.flash-sale-title i {
    color: #fde047;
    font-size: 1.5rem;
    margin-right: 0.75rem;
}

.flash-sale-title h2 {
    font-size: 1.5rem;
    font-weight: bold;
}

.countdown {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255,255,255,0.2);
    backdrop-filter: blur(4px);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 500;
}

.time-box {
    background: white;
    color: #dc2626;
    font-weight: bold;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

@media (min-width: 768px) {
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1024px) {
    .products-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

.products-grid.featured {
    grid-template-columns: repeat(1, 1fr);
}

@media (min-width: 640px) {
    .products-grid.featured {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .products-grid.featured {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1024px) {
    .products-grid.featured {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Product Card */
.product-card {
    background: white;
    border: 2px solid #f3f4f6;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 1rem;
    position: relative;
    transition: all 0.3s ease;
}

.product-card:hover {
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    border-color: #dbeafe;
}

.product-card.flash-sale:hover {
    border-color: #fecaca;
}

.discount-badge {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: #dc2626;
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.product-image {
    margin-bottom: 0.75rem;
    display: flex;
    justify-content: center;
}

.product-image img {
    width: 144px;
    height: 144px;
    object-fit: contain;
}

.featured .product-image img {
    width: 192px;
    height: 192px;
}

.product-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 0.5rem;
    height: 2.5rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.featured .product-name {
    font-size: 1rem;
    height: 3rem;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.new-price {
    color: #dc2626;
    font-weight: bold;
}

.old-price {
    color: #9ca3af;
    font-size: 0.75rem;
    text-decoration: line-through;
}

.single-price {
    color: #dc2626;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.progress-bar {
    margin-top: 0.75rem;
    background: #fecaca;
    border-radius: 9999px;
    height: 8px;
    overflow: hidden;
}

.progress-fill {
    background: #dc2626;
    height: 100%;
    border-radius: 9999px;
}

.sold-count {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.rating {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.stars {
    display: flex;
    margin-right: 0.5rem;
}

.stars i {
    color: #fbbf24;
}

.review-count {
    color: #6b7280;
    font-size: 0.875rem;
}

.product-btn {
    width: 100%;
    background: #dc2626;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 0.75rem;
}

.product-btn:hover {
    background: #b91c1c;
}

.product-btn.featured {
    background: #2563eb;
}

.product-btn.featured:hover {
    background: #1d4ed8;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding-top: 3rem;
    padding-bottom: 1.5rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

@media (min-width: 768px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
}

.footer-column h3 {
    font-size: 1.125rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: 0.5rem;
}

.footer-column ul li a {
    color: #d1d5db;
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.footer-column ul li a:hover {
    color: white;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
}

.social-link.facebook {
    background: #2563eb;
}

.social-link.facebook:hover {
    background: #1d4ed8;
}

.social-link.youtube {
    background: #dc2626;
}

.social-link.youtube:hover {
    background: #b91c1c;
}

.social-link.instagram {
    background: #db2777;
}

.social-link.instagram:hover {
    background: #be185d;
}

.social-link.twitter {
    background: #60a5fa;
}

.social-link.twitter:hover {
    background: #3b82f6;
}

.newsletter {
    display: flex;
}

.newsletter input {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border: none;
    border-radius: 0.25rem 0 0 0.25rem;
    color: #1f2937;
    font-size: 0.875rem;
    outline: none;
}

.newsletter input:focus {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.newsletter button {
    background: #2563eb;
    border: none;
    padding: 0 1rem;
    border-radius: 0 0.25rem 0.25rem 0;
    color: white;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.newsletter button:hover {
    background: #1d4ed8;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

@media (min-width: 768px) {
    .footer-bottom {
        flex-direction: row;
        justify-content: space-between;
    }
}

.payment-methods h3,
.shipping-methods h3 {
    font-size: 1.125rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.payment-icons,
.shipping-icons {
    display: flex;
    gap: 1rem;
}

.payment-icons i,
.shipping-icons i {
    font-size: 1.5rem;
}

.copyright {
    text-align: center;
    margin-top: 2rem;
    color: #9ca3af;
    font-size: 0.875rem;
}

.copyright p {
    margin-bottom: 0.25rem;
}

/* Responsive Design */
@media (max-width: 640px) {
    .header-content {
        flex-wrap: wrap;
        height: auto;
        padding: 0.5rem 0;
    }

    .search-container {
        order: 3;
        width: 100%;
        margin: 0.5rem 0 0 0;
    }

    .banner-content h2 {
        font-size: 1.5rem;
    }

    .banner-content p {
        font-size: 1rem;
    }

    .categories-list {
        gap: 1rem;
    }

    .flash-sale-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="./index.css" />
  </head>
  <body>
    <h1 id="test">xin chào tôi là trường đây</h1>
    <div>
      <p id="noidung">Đ<PERSON>y là đoạn văn</p>
    </div>
    <script src="./index.js"></script>

    <div>
      <h2 id="test01">Đ<PERSON>y là đoạn văn 2</h2>
      <h2 id="test02">Đ<PERSON>y là đoạn văn 2</h2>

      <button type="button" onclick="document.write(5+6)">
        Đổi bằng onclick
      </button>

      <button type="button" onclick="document.getElementById('test02').innerHTML = myFunction()">Click 02</button>
    </div>

    <div class="khung01" onmouseover="myFunction()" onmouseout="myFunctionout()">
      <h2 id="test03">Đây là đoạn văn 3</h2>
    </div>
    <div class="khung01" onmouseover="myFunction()" onmouseout="myFunctionout()">
      <h2 id="test03">Đây là đoạn văn 3</h2>
    </div>
    <div class="khung01" onmouseover="myFunction()" onmouseout="myFunctionout()">
      <h2 id="test03">Đây là đoạn văn 3</h2>
    </div>


  </body>
</html>

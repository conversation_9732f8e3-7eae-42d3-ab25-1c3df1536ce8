<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="./index.css" />
  </head>
  <body>
  
    <url>https://www.youtube.com/watch?v=n8pyentNuQg&list=PLPt6-BtUI22pYwpfmkP4EuJkf6GRe63KU&index=10</url>

    <h1 id="test">xin chào tôi là trường đây</h1>
    <div>
      <p id="noidung">Đây là đoạn văn</p>
    </div>
    <script src="./index.js"></script>

    <div>
      <h2 id="test01">Đây là đoạn văn 2</h2>
      <h2 id="test02">Đ<PERSON><PERSON> là đoạn văn 2</h2>

      <button type="button" onclick="document.write(5+6)">
        Đổi bằng onclick
      </button>

      <button type="button" onclick="document.getElementById('test02').innerHTML = myFunction()">Click 02</button>
    </div>

    <div id="khung02">
       <div class="khung01" onmouseover="myFunction(this)" onmouseout="myFunctionout(this)">
      <h2 id="test03">Đây là đoạn văn 3</h2>
      </div>
      <div class="khung01" onmouseover="myFunction(this)" onmouseout="myFunctionout(this)">
      <h2 id="test04">Đây là đoạn văn 4</h2>
      </div>
      <div class="khung01" onmouseover="myFunction(this)" onmouseout="myFunctionout(this)">
      <h2 id="test05">Đây là đoạn văn 5</h2>
      </div>
    </div>

    <div>
      <h2 id="test06">Đây là đoạn văn 6</h2>
      <button type="button" onclick = myCar() >Click 03</button>
    </div>

   


  </body>
</html>

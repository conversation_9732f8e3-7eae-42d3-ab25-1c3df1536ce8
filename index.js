
document.getElementById("test").innerHTML = "Nội dung đã đổi bằng file js"

document.getElementById("noidung").innerHTML = "<h2>Đ<PERSON>y là đoạn văn đã đổi bằng file js và thêm thẻ h2</h2>"

let x, y, z;
x = 5;
y = 16;
z = x + y;

let car = {
    name: "Car01",
    color: "red",
    year: 2020,
    long: 200,
    price: 1000,
    lop: 15,
}

let myArray = [
    "Car01",
    "Car02",
    "Car03" 
    
]


function myCar(){

    if (myArray.length > 5){
         let text = ""
    for (let x of myArray){
        text += x + "<br>";
    }

    document.getElementById("test06").innerHTML = text;
    }
    else{
        document.getElementById("test06").innerHTML = "so xe it hon 5 nen khong hien thi";
    }

   
}



function myFunction(element){
    element.style.backgroundColor = 'red';
}

function myFunctionout(element){
    element.style.backgroundColor = 'yellow';
}

// vòng lập

function test(x){
    for (let i = 0; i < x; i++) {
    document.getElementById("test06").innerHTML += i + "<br>";
    
}
}

for (let i = 0; i < 10; i++) {
    console.log(i);
    
}

// khai báo biến

let bien = "tần quan nhã";
let bi01 = 10;
let b3 = `gia tri so thu nhat la: ${bien}, gia tri bien 2 là: ${bi01}`

function test02(){
    khong = b3;
    document.getElementById("test04").innerHTML = khong;
}



